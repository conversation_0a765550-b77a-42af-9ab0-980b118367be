{"input": {"source_path": "data/inputs/doctor.mp4", "source_format": "mp4", "temp_dir": "./tmp", "silence": {"method": "energy", "threshold": 0.02, "padding_seconds": 0.25}, "separation": {"model": "htdemucs_ft", "stems": ["vocals", "accompaniment"], "output_format": "wav", "segment_duration": 30.0, "extra_args": []}}, "translation": {"source_lang": "en", "target_lang": "es", "analyzer": {"provider": "gemini", "model": "gemini-2.5-pro", "temperature": 0.2, "top_p": 0.95}, "segment_processor": {"provider": "gemini", "model": "gemini-2.5-pro", "temperature": 0.2, "top_p": 0.95}}, "performance": {"max_workers": 4, "rate_limit_per_sec": 2, "use_gpu": true, "max_memory_gb": null}, "models": {"tts_provider": "yandex", "transcription_chunk_size": 30.0, "timestamp_window": 0.5, "tts_voice": "eleven_multilingual_v2", "temperature": 0.7}, "output": {"output_dir": "./outputs", "naming_pattern": "{basename}_{lang}.{ext}", "log_level": "info", "log_format": "%(asctime)s | %(levelname)s | %(message)s", "video_encoder": "libx264"}}