{"_comment": "aizen platform dubbing configuration - production-ready example", "_description": "this configuration provides realistic defaults for video dubbing workflows. copy this file to 'run_config.json' and modify values as needed for your specific use case.", "input": {"_comment": "input media processing settings", "source_path": "data/inputs/sample_video.mp4", "source_format": "mp4", "temp_dir": "./tmp", "silence": {"_comment": "silence detection for natural speech segmentation", "method": "energy", "threshold": 0.02, "padding_seconds": 0.25}, "separation": {"_comment": "audio source separation using demucs - htdemucs_ft provides best quality but is 4x slower than htdemucs", "model": "htdemucs_ft", "stems": ["vocals", "accompaniment"], "output_format": "wav", "segment_duration": 30.0, "extra_args": []}}, "translation": {"_comment": "translation pipeline configuration using google gemini models", "source_lang": "en", "target_lang": "es", "analyzer": {"_comment": "gemini-2.0-flash-001 is faster and cheaper than gemini-2.5-pro while maintaining good quality", "provider": "gemini", "model": "gemini-2.0-flash-001", "temperature": 0.2, "top_p": 0.95}, "segment_processor": {"_comment": "same model for consistency - use gemini-2.5-pro for highest quality if budget allows", "provider": "gemini", "model": "gemini-2.0-flash-001", "temperature": 0.2, "top_p": 0.95}}, "performance": {"_comment": "resource management and processing limits", "max_workers": 4, "rate_limit_per_sec": 2, "use_gpu": true, "max_memory_gb": null}, "models": {"_comment": "text-to-speech and transcription model settings", "tts_provider": "elevenlabs", "transcription_chunk_size": 30.0, "timestamp_window": 0.5, "tts_voice": "eleven_multilingual_v2", "temperature": 0.7}, "output": {"_comment": "output file configuration and logging settings", "output_dir": "./outputs", "naming_pattern": "{basename}_{lang}.{ext}", "log_level": "info", "log_format": "%(asctime)s | %(levelname)s | %(message)s", "video_encoder": "libx264"}}