from pathlib import Path

from src.config import load_config, RunConfig

EXAMPLE_CONFIG_PATH = Path(__file__).parent.parent / "run_config.example.json"


def test_load_example_config():
    cfg = load_config(EXAMPLE_CONFIG_PATH)
    assert isinstance(cfg, RunConfig)
    assert cfg.input.source_path.name == "example.mp4"
    assert cfg.models.tts_provider in {"azure", "yandex", "elevenlabs"}
    assert cfg.output.video_encoder == "libx264"


def test_roundtrip_serialization(tmp_path):
    cfg = load_config(EXAMPLE_CONFIG_PATH)
    tmp_file = tmp_path / "tmp_config.json"
    tmp_file.write_text(cfg.model_dump_json())
    cfg2 = load_config(tmp_file)
    assert cfg2 == cfg
