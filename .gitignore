# python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.venv

# virtual environment
venv/
env/
ENV/
.env
.venv
.python-version

# ide
.idea/
.vscode/
*.swp
*.swo
*~
.project
.pydevproject
.settings/
.vs/

# testing
.pytest_cache/
.coverage
coverage.xml
htmlcov/
.tox/
nosetests.xml
coverage/

# logs
logs/
*.log
dubbing_logger.log*
open_dubbing.log*

# project specific
data/outputs/*
data/temp/
data/cache/
*.mp3
*.mp4
*.wav
*.srt
*.vtt
!config/*.json

# documentation
docs/_build/
site/

# jupyter notebook
.ipynb_checkpoints
*.ipynb

# macos
.DS_Store
.AppleDouble
.LSOverride
._*

# windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# model files and cache
*.pth
*.onnx
*.pt
*.bin
.cache/
pretrained/
*.model

# temporary files
tmp/
temp/
*.tmp
*.bak
*.swp
*.swo

# dependencies
node_modules/
jspm_packages/
bower_components/

# distribution
dist/
build/
*.pyc

# environment variables
.env
.env.*
!.env.example

# docker
.docker/
docker-compose.override.yml

# profiling
*.prof
*.stats
profile/

# security
*.pem
*.key
*.cert
credentials.json
token.json
secrets.yaml
.secrets/
