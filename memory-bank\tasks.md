# tasks

> this file is used for active, in-progress task tracking, detailing steps, checklists, and component lists.

## completed tasks

-   **bug fix: ensure separated audio/video files are saved inside run output directory**
    -   **status:** completed & archived
    -   **archive:** [archive-filepath-safety.md](memory-bank/archive/archive-filepath-safety.md)

## current task

**feature: unified run configuration**

### requirements analysis

consolidate all scattered configuration parameters into a single json file (`run_config.json`) that covers the full processing pipeline. the file must include five logical sections:

1. input processing configuration
2. translation pipeline configuration
3. performance and resource management
4. model configurations
5. output and logging configuration

the system must load this json at runtime and propagate its parameters through all pipeline stages without hard-coded values.

### components affected

-   `src/core/dubbing.py` (main orchestration)
-   `src/core/command_line.py` (cli entry)
-   `src/audio_processing/*` modules (audio separation, tts, stt)
-   `src/translation/*` modules
-   `src/video_processing/*` modules
-   `src/utils/logger.py`

### architecture considerations

1. introduce a new module `src/config.py` containing:
    - a pydantic `basemodel` hierarchy describing the unified schema.
    - a `load_config(path: str) -> RunConfig` helper that reads json and returns a validated object.
2. deprecate any direct environment variable or inline default usage inside processing modules; instead accept the appropriate section of the loaded `RunConfig` via dependency injection.
3. ensure backwards compatibility by providing sensible defaults when keys are missing and allowing cli overrides for quick testing.

### implementation strategy

1. design pydantic models reflecting the five sections with nested models.
2. create a default `run_config.example.json` file in project root.
3. modify `command_line.py` to accept `--config /path/to/config.json` (default to example file) and call `load_config`.
4. refactor `dubbing.py` constructor to receive a `RunConfig` object and pass sub-configs to downstream components.
5. update each affected module to accept its respective config subset rather than individual parameters.
6. update logger setup to use config-defined log level and format.
7. add documentation to `README.md` explaining the unified configuration usage.

### detailed steps

1. **schema design**
    - define `InputProcessingConfig`, `TranslationConfig`, `PerformanceConfig`, `ModelConfig`, `OutputConfig` classes.
    - assemble them into top-level `RunConfig`.
2. **config loader**
    - implement `load_config` with error handling and clear validation messages.
3. **cli integration**
    - add `--config` option with path validation.
4. **pipeline refactor**
    - pass `RunConfig` into `Dubber` (in `dubbing.py`).
    - adjust audio, translation, video modules to consume config.
5. **logging update**
    - read log level/format from `OutputConfig` when configuring root logger.
6. **testing**
    - create unit tests for `load_config` validation.
    - run full pipeline using example config to ensure no regressions.

### dependencies

-   add `pydantic` to `pyproject.toml` (using uv lock update).

### challenges & mitigations

| challenge               | mitigation                                                                                                             |
| ----------------------- | ---------------------------------------------------------------------------------------------------------------------- |
| large refactor risk     | adopt incremental updates: introduce config injection while keeping old parameters optional, then clean up once stable |
| maintaining defaults    | embed `model_config` defaults inside pydantic models                                                                   |
| breakage across modules | add extensive type checking/mypy session to catch missing params                                                       |

### creative phase components

schema design for `RunConfig` requires thoughtful structuring and naming; flag for creative review.

### checklist

-   [ ] design pydantic schema
-   [ ] implement `src/config.py` with loader
-   [ ] create `run_config.example.json`
-   [ ] add cli `--config` option
-   [ ] integrate config into core pipeline
-   [ ] refactor downstream modules to consume config
-   [ ] update logger configuration
-   [ ] update `README.md`
-   [ ] add unit tests for config loading

### components

-   `InputProcessingConfig`
-   `TranslationConfig`
-   `PerformanceConfig`
-   `ModelConfig`
-   `OutputConfig`
-   `RunConfig`

### notes

this feature spans multiple modules therefore classified as **level 3 – intermediate feature**. after creative review, implementation will proceed in build mode following qa validation.
